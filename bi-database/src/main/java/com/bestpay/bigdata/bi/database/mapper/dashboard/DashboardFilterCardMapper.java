package com.bestpay.bigdata.bi.database.mapper.dashboard;

import com.bestpay.bigdata.bi.common.dto.dashboard.FilterCardQueryDTO;
import com.bestpay.bigdata.bi.database.dao.dashboard.DashboardFilterCardDO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface DashboardFilterCardMapper {

    Long insert(DashboardFilterCardDO dashboardFilterCard);

    DashboardFilterCardDO findById(Long id);

    List<DashboardFilterCardDO> find(FilterCardQueryDTO cardQueryDTO);


    void update(@Param("dashboardId") Long dashboardId,
                @Param("statusCode") int statusCode,
                @Param("updatedBy") String updatedBy);

    void updateAll(DashboardFilterCardDO dashboardFilterCard);

    // 新增备份表操作方法
    List<DashboardFilterCardDO> findBak();

    void batchInsert(@Param("filterCardList") List<DashboardFilterCardDO> filterCardList);
}
